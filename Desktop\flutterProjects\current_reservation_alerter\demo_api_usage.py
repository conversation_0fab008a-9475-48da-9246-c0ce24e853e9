#!/usr/bin/env python3
"""
Demo script showing how the Flutter app integrates with Google Apps Script
This demonstrates the exact same API calls that the Flutter app makes.
"""

import requests
import json


def demo_submit_train_selections():
    """Demo: Submit train selections to Google Apps Script (equivalent to Flutter's submitTrainSelections)"""
    print("=== DEMO: Submit Train Selections ===")
    
    # 1. PASTE YOUR DEPLOYED GOOGLE APPS SCRIPT URL HERE
    script_url = "https://script.google.com/macros/s/AKfycbziuBtNY7OLALD_94LkpF44rqQ7Af-0wUCGJEW-7DxE_vnDeiCl1jS9JVDSG7eS-Qf_/exec"

    # 2. This is the exact JSON payload your Flutter app will send.
    payload_data = [
        {
            "userId": "RKQ1.201217.002",
            "telegramId": "123456789",
            "fromStation": "CHIRALA - CLX",
            "toStation": "SECUNDERABAD JN - SC",
            "journeyDate": "20-07-2025",
            "trainNo": "17229",
            "chartPrepTime": "2025-07-25 15:42:48",
            "selectedClasses": "SL,3A"
        },
        {
            "userId": "RKQ1.201217.002",
            "telegramId": "123456789",
            "fromStation": "CHIRALA - CLX",
            "toStation": "SECUNDERABAD JN - SC",
            "journeyDate": "20-07-2025",
            "trainNo": "12709",
            "chartPrepTime": "2025-07-26 10:39:34",
            "selectedClasses": "SL,3E"
        },
        {
            "userId": "RKQ1.201217.002",
            "telegramId": "123456789",
            "fromStation": "CHIRALA - CLX",
            "toStation": "SECUNDERABAD JN - SC",
            "journeyDate": "20-07-2025",
            "trainNo": "12603",
            "chartPrepTime": "2025-07-27 08:48:50",
            "selectedClasses": "SL,3A"
        }
    ]

    # 3. Set the headers to indicate you are sending JSON data.
    headers = {
        "Content-Type": "application/json"
    }

    print(f"Sending POST request to: {script_url}")

    try:
        # 4. Make the HTTP POST request.
        # The `json` parameter automatically converts the Python list of dicts to a JSON string.
        response = requests.post(script_url, headers=headers, json=payload_data)

        # 5. Print the results.
        print(f"\nStatus Code: {response.status_code}")
        print("Response from Google Apps Script:")
        # Pretty-print the JSON response from the script
        print(json.dumps(response.json(), indent=2))

        if response.status_code == 200:
            print("\nSUCCESS: Check your 'Alerts' Google Sheet. You should see 3 new rows.")
        else:
            print("\nERROR: The request failed. The response above may contain details.")

    except requests.exceptions.RequestException as e:
        print(f"\nAn error occurred during the request: {e}")


def demo_fetch_user_logs():
    """Demo: Fetch user logs from Google Apps Script (equivalent to Flutter's fetchUserLogs)"""
    print("\n\n=== DEMO: Fetch User Logs ===")
    
    # 1. PASTE YOUR DEPLOYED GOOGLE APPS SCRIPT URL HERE
    script_url = "https://script.google.com/macros/s/AKfycbziuBtNY7OLALD_94LkpF44rqQ7Af-0wUCGJEW-7DxE_vnDeiCl1jS9JVDSG7eS-Qf_/exec"

    # 2. Specify the unique User ID for which you want to retrieve logs.
    # This should be a User ID that you expect to have entries in your 'Logs' sheet.
    user_id_to_fetch = "RKQ1.201217.002"

    # 3. Construct the full URL with the userId as a query parameter.
    request_url = f"{script_url}?userId={user_id_to_fetch}"

    print(f"Sending GET request to retrieve logs for user '{user_id_to_fetch}'...")
    print(f"Request URL: {request_url}")

    try:
        # 4. Make the HTTP GET request. No headers or body are needed.
        response = requests.get(request_url)

        # 5. Print the results.
        print(f"\nStatus Code: {response.status_code}")
        print("Response from Google Apps Script:")
        
        # Check if the response content is not empty before trying to parse JSON
        if response.text:
            # Pretty-print the JSON response from the script
            print(json.dumps(response.json(), indent=2))
            
            if response.status_code == 200 and response.json().get("status") == "success":
                log_count = len(response.json().get("logs", []))
                print(f"\nSUCCESS: Successfully retrieved {log_count} log entries.")
            else:
                print("\nERROR: The request was processed, but the script returned an error status.")
                print("Check the 'message' field in the response above for details.")
        else:
            print("Response was empty.")

    except requests.exceptions.RequestException as e:
        print(f"\nAn error occurred during the request: {e}")


def main():
    """Run both demos to show the complete API integration"""
    print("Flutter App API Integration Demo")
    print("=" * 50)
    print("This script demonstrates the exact same API calls that the Flutter app makes.")
    print("The Flutter app uses Dio (HTTP client) to make these same requests.")
    print()
    
    # Demo 1: Submit train selections (equivalent to what happens when user completes selection)
    demo_submit_train_selections()
    
    # Demo 2: Fetch user logs (equivalent to what happens in the User Logs screen)
    demo_fetch_user_logs()
    
    print("\n" + "=" * 50)
    print("Demo completed!")
    print("\nIn the Flutter app:")
    print("1. When user completes train selection -> calls submitTrainSelections()")
    print("2. When user opens User Logs screen -> calls fetchUserLogs()")


if __name__ == "__main__":
    main()
