import 'package:flutter/material.dart';

class ResponseScreen extends StatelessWidget {
  final String responseText;
  const ResponseScreen({super.key, required this.responseText});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("API Response"),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: SelectableText(
          responseText,
          style: const TextStyle(fontFamily: 'monospace'),
        ),
      ),
    );
  }
}
