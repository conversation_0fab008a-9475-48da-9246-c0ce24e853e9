import 'package:flutter/material.dart';
import 'dart:convert';
import 'models/train_data.dart';
import 'multiple_train_selection_screen.dart';

class TrainResultsScreen extends StatefulWidget {
  final String responseText;
  final String fromStation;
  final String toStation;
  final String journeyDate;

  const TrainResultsScreen({
    super.key,
    required this.responseText,
    required this.fromStation,
    required this.toStation,
    required this.journeyDate,
  });

  @override
  State<TrainResultsScreen> createState() => _TrainResultsScreenState();
}

class _TrainResultsScreenState extends State<TrainResultsScreen> {
  TrainSearchResponse? trainResponse;
  String? errorMessage;
  Set<String> selectedTrainNumbers = {};

  @override
  void initState() {
    super.initState();
    _parseTrainData();
  }

  void _parseTrainData() {
    try {
      final jsonData = jsonDecode(widget.responseText);
      setState(() {
        trainResponse = TrainSearchResponse.fromJson(jsonData);
      });
    } catch (e) {
      setState(() {
        errorMessage = 'Failed to parse train data: ${e.toString()}';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Available Trains'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: errorMessage != null
          ? _buildErrorView()
          : trainResponse == null
              ? const Center(child: CircularProgressIndicator())
              : _buildTrainList(),
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              'Error',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              errorMessage!,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Go Back'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrainList() {
    final trains = trainResponse!.trainBtwnStnsList;
    
    if (trains.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.train, size: 64, color: Colors.grey),
              const SizedBox(height: 16),
              Text(
                'No Trains Found',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              const Text(
                'No trains are available for the selected route and date.',
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Search Again'),
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      children: [
        // Header with route info
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16.0),
          color: Colors.blue.shade50,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '${widget.fromStation} → ${widget.toStation}',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Journey Date: ${widget.journeyDate}',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade700,
                ),
              ),
              Text(
                '${trains.length} trains found',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade700,
                ),
              ),
            ],
          ),
        ),
        
        // Train list
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(8.0),
            itemCount: trains.length,
            itemBuilder: (context, index) {
              final train = trains[index];
              return _buildTrainCard(train);
            },
          ),
        ),

        // Bottom action bar
        if (selectedTrainNumbers.isNotEmpty)
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.3),
                  spreadRadius: 1,
                  blurRadius: 5,
                  offset: const Offset(0, -3),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    '${selectedTrainNumbers.length} train(s) selected',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                ElevatedButton(
                  onPressed: _proceedWithSelectedTrains,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Proceed'),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildTrainCard(TrainData train) {
    final isSelected = selectedTrainNumbers.contains(train.trainNumber);

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 8.0),
      elevation: 2,
      color: isSelected ? Colors.blue.shade50 : null,
      child: InkWell(
        onTap: () => _toggleTrainSelection(train.trainNumber),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Train name and number with checkbox
              Row(
                children: [
                  Checkbox(
                    value: isSelected,
                    onChanged: (bool? value) {
                      _toggleTrainSelection(train.trainNumber);
                    },
                    activeColor: Colors.blue,
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${train.trainNumber} - ${train.trainName}',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: isSelected ? Colors.blue.shade700 : Colors.blue,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          train.trainType.join(', '),
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.green.shade100,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      train.runningDays,
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.green.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Timing and duration
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Departure',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        Text(
                          train.departureTime,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          train.fromStnCode,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  Column(
                    children: [
                      Text(
                        train.duration,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.orange,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        width: 60,
                        height: 2,
                        color: Colors.grey.shade300,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${train.distance} km',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                  
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          'Arrival',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        Text(
                          train.arrivalTime,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          train.toStnCode,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Available classes
              Wrap(
                spacing: 6,
                runSpacing: 4,
                children: train.avlClasses.map((cls) {
                  final isAutoSelected = train.autoSelectedClasses.contains(cls);
                  return Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: isAutoSelected ? Colors.blue.shade100 : Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(12),
                      border: isAutoSelected 
                          ? Border.all(color: Colors.blue.shade300)
                          : null,
                    ),
                    child: Text(
                      cls,
                      style: TextStyle(
                        fontSize: 12,
                        color: isAutoSelected ? Colors.blue.shade700 : Colors.grey.shade700,
                        fontWeight: isAutoSelected ? FontWeight.w600 : FontWeight.normal,
                      ),
                    ),
                  );
                }).toList(),
              ),
              
              const SizedBox(height: 8),

              // Selection status
              if (isSelected)
                Align(
                  alignment: Alignment.centerRight,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade100,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.check_circle, size: 16, color: Colors.blue.shade700),
                        const SizedBox(width: 4),
                        Text(
                          'Selected',
                          style: TextStyle(
                            color: Colors.blue.shade700,
                            fontWeight: FontWeight.w500,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  void _toggleTrainSelection(String trainNumber) {
    setState(() {
      if (selectedTrainNumbers.contains(trainNumber)) {
        selectedTrainNumbers.remove(trainNumber);
      } else {
        selectedTrainNumbers.add(trainNumber);
      }
    });
  }

  void _proceedWithSelectedTrains() {
    if (selectedTrainNumbers.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select at least one train'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // Get selected trains data
    final selectedTrains = trainResponse!.trainBtwnStnsList
        .where((train) => selectedTrainNumbers.contains(train.trainNumber))
        .toList();

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => MultipleTrainSelectionScreen(
          trains: selectedTrains,
          fromStation: widget.fromStation,
          toStation: widget.toStation,
          journeyDate: widget.journeyDate,
        ),
      ),
    );
  }
}
