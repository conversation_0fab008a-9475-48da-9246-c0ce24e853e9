import 'package:flutter/material.dart';
import 'dart:typed_data';

Future<String?> showCaptchaDialog(BuildContext context, List<int> captchaImageBytes) async {
  final captchaController = TextEditingController();
  
  return showDialog<String>(
    context: context,
    barrierDismissible: false, // User must enter captcha or cancel
    builder: (BuildContext context) {
      return AlertDialog(
        title: const Text('Enter CAPTCHA'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('Please enter the text you see in the image below.'),
              const SizedBox(height: 16),
              // The image is displayed on a white background for clarity
              Container(
                color: Colors.white,
                padding: const EdgeInsets.all(8.0),
                child: Image.memory(Uint8List.fromList(captchaImageBytes)),
              ),
              const Sized<PERSON>ox(height: 16),
              TextField(
                controller: captchaController,
                autofocus: true,
                decoration: const InputDecoration(
                  labelText: 'CAPTCHA Value',
                  border: OutlineInputBorder(),
                ),
              ),
            ],
          ),
        ),
        actions: <Widget>[
          TextButton(
            child: const Text('Cancel'),
            onPressed: () {
              Navigator.of(context).pop(); // Return null
            },
          ),
          ElevatedButton(
            child: const Text('Submit'),
            onPressed: () {
              if (captchaController.text.isNotEmpty) {
                Navigator.of(context).pop(captchaController.text); // Return the entered text
              }
            },
          ),
        ],
      );
    },
  );
}
