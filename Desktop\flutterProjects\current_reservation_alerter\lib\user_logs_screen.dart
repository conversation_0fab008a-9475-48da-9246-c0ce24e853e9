import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:convert';
import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'rail_api_service.dart';

class UserLogsScreen extends StatefulWidget {
  const UserLogsScreen({super.key});

  @override
  State<UserLogsScreen> createState() => _UserLogsScreenState();
}

class _UserLogsScreenState extends State<UserLogsScreen> {
  final RailApiService _apiService = RailApiService();
  final _userIdController = TextEditingController();
  
  List<Map<String, dynamic>> _logs = [];
  bool _isLoading = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _getDeviceId();
  }

  @override
  void dispose() {
    _userIdController.dispose();
    super.dispose();
  }

  Future<void> _getDeviceId() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      String deviceId;
      
      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        deviceId = androidInfo.id;
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        deviceId = iosInfo.identifierForVendor ?? 'unknown-ios';
      } else {
        deviceId = 'unknown-platform';
      }
      
      setState(() {
        _userIdController.text = deviceId; // Pre-fill with device ID
      });
    } catch (e) {
      setState(() {
        _userIdController.text = 'unknown-device';
      });
    }
  }

  Future<void> _fetchUserLogs() async {
    final userId = _userIdController.text.trim();
    if (userId.isEmpty) {
      setState(() {
        _error = 'Please enter a User ID';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _error = null;
      _logs.clear();
    });

    try {
      final result = await _apiService.fetchUserLogs(userId);
      
      if (result['status'] == 'success') {
        final logs = result['logs'] as List<dynamic>? ?? [];
        setState(() {
          _logs = logs.cast<Map<String, dynamic>>();
          _isLoading = false;
        });
      } else {
        setState(() {
          _error = result['message'] ?? 'Failed to fetch logs';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  void _showLogDetails(Map<String, dynamic> log) {
    const jsonEncoder = JsonEncoder.withIndent('  ');
    final jsonString = jsonEncoder.convert(log);

    // Get train number using the correct field name
    final trainNo = log['TrainNo'] ?? log['trainNo'] ?? 'Unknown Train';
    final finalStatus = log['FinalStatus'] ?? log['finalStatus'] ?? 'Unknown Status';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Log Details - Train $trainNo ($finalStatus)'),
        content: SingleChildScrollView(
          child: Container(
            width: double.maxFinite,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(8),
            ),
            child: SelectableText(
              jsonString,
              style: const TextStyle(
                fontFamily: 'monospace',
                fontSize: 12,
              ),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Clipboard.setData(ClipboardData(text: jsonString));
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Copied to clipboard')),
              );
            },
            child: const Text('Copy'),
          ),
        ],
      ),
    );
  }

  Widget _buildUserIdField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'User ID',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _userIdController,
          decoration: InputDecoration(
            labelText: 'Enter User ID to fetch logs',
            border: const OutlineInputBorder(),
            prefixIcon: const Icon(Icons.person),
            helperText: 'Default: Your device ID',
            suffixIcon: IconButton(
              icon: const Icon(Icons.search),
              onPressed: _fetchUserLogs,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLogsList() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Fetching user logs...'),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red.shade300),
            const SizedBox(height: 16),
            Text(
              'Error: $_error',
              style: const TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _fetchUserLogs,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_logs.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inbox_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No logs found',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              'Enter a User ID and tap search to fetch logs',
              style: TextStyle(color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _logs.length,
      itemBuilder: (context, index) {
        final log = _logs[index];
        return _buildLogCard(log);
      },
    );
  }

  Widget _buildLogCard(Map<String, dynamic> log) {
    // Handle the actual field names from your Google Apps Script response
    final trainNo = log['TrainNo'] ?? log['trainNo'] ?? 'Unknown';
    final fromStation = log['FromStation'] ?? log['fromStation'] ?? 'Unknown';
    final toStation = log['ToStation'] ?? log['toStation'] ?? 'Unknown';
    final journeyDate = log['JourneyDate'] ?? log['journeyDate'] ?? 'Unknown';
    final selectedClasses = log['ClassesQueried'] ?? log['selectedClasses'] ?? 'Unknown';
    final telegramId = log['TelegramID'] ?? log['telegramId'] ?? 'Unknown';
    final logTimestamp = log['LogTimestamp'] ?? log['logTimestamp'] ?? 'Unknown';
    final finalStatus = log['FinalStatus'] ?? log['finalStatus'] ?? 'Unknown';
    final details = log['Details'] ?? log['details'] ?? 'No details available';

    // Format timestamp for display (convert UTC to local time)
    String formattedTimestamp = 'Unknown';
    if (logTimestamp != 'Unknown') {
      try {
        final utcDateTime = DateTime.parse(logTimestamp);
        final localDateTime = utcDateTime.toLocal();
        formattedTimestamp = '${localDateTime.day.toString().padLeft(2, '0')}-${localDateTime.month.toString().padLeft(2, '0')}-${localDateTime.year} ${localDateTime.hour.toString().padLeft(2, '0')}:${localDateTime.minute.toString().padLeft(2, '0')}';
      } catch (e) {
        formattedTimestamp = logTimestamp;
      }
    }

    // Determine status color
    Color statusColor = Colors.grey;
    IconData statusIcon = Icons.info;
    if (finalStatus.toLowerCase().contains('notified')) {
      statusColor = Colors.green;
      statusIcon = Icons.check_circle;
    } else if (finalStatus.toLowerCase().contains('expired') || finalStatus.toLowerCase().contains('stale')) {
      statusColor = Colors.orange;
      statusIcon = Icons.schedule;
    } else if (finalStatus.toLowerCase().contains('not available')) {
      statusColor = Colors.red;
      statusIcon = Icons.cancel;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _showLogDetails(log),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      'Train $trainNo',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                  ),
                  Row(
                    children: [
                      Icon(statusIcon, size: 16, color: statusColor),
                      const SizedBox(width: 4),
                      Text(
                        finalStatus,
                        style: TextStyle(
                          fontSize: 12,
                          color: statusColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(width: 8),
                  Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey.shade600),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: Text(
                      'From: $fromStation',
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ],
              ),
              Row(
                children: [
                  Expanded(
                    child: Text(
                      'To: $toStation',
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: Text(
                      'Journey: $journeyDate',
                      style: const TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      'Classes: $selectedClasses',
                      style: const TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                'Logged: $formattedTimestamp',
                style: const TextStyle(fontSize: 12, color: Colors.grey),
              ),
              Text(
                'Telegram ID: $telegramId',
                style: const TextStyle(fontSize: 12, color: Colors.grey),
              ),
              if (details != 'No details available') ...[
                const SizedBox(height: 4),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: statusColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(color: statusColor.withValues(alpha: 0.3)),
                  ),
                  child: Text(
                    details,
                    style: TextStyle(
                      fontSize: 12,
                      color: statusColor.withValues(alpha: 0.8),
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('User Logs'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _fetchUserLogs,
            tooltip: 'Refresh logs',
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            _buildUserIdField(),
            const SizedBox(height: 24),
            if (_logs.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: Row(
                  children: [
                    Text(
                      'Found ${_logs.length} log(s)',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Colors.blue,
                      ),
                    ),
                    const Spacer(),
                    TextButton.icon(
                      onPressed: () {
                        const jsonEncoder = JsonEncoder.withIndent('  ');
                        final jsonString = jsonEncoder.convert(_logs);
                        Clipboard.setData(ClipboardData(text: jsonString));
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('All logs copied to clipboard')),
                        );
                      },
                      icon: const Icon(Icons.copy, size: 16),
                      label: const Text('Copy All'),
                    ),
                  ],
                ),
              ),
            Expanded(child: _buildLogsList()),
          ],
        ),
      ),
    );
  }
}
