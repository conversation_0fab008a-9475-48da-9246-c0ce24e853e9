import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:convert';
import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'models/train_data.dart';
import 'rail_api_service.dart';

class TrainSelectionScreen extends StatefulWidget {
  final TrainData train;
  final String fromStation;
  final String toStation;
  final String journeyDate;

  const TrainSelectionScreen({
    super.key,
    required this.train,
    required this.fromStation,
    required this.toStation,
    required this.journeyDate,
  });

  @override
  State<TrainSelectionScreen> createState() => _TrainSelectionScreenState();
}

class _TrainSelectionScreenState extends State<TrainSelectionScreen> {
  final _telegramIdController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  final RailApiService _apiService = RailApiService();
  
  Set<String> _selectedClasses = {};
  bool _isLoadingChartTime = false;
  String? _chartTime;
  String? _deviceId;
  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    _initializeSelection();
    _getDeviceId();
  }

  @override
  void dispose() {
    _telegramIdController.dispose();
    super.dispose();
  }

  void _initializeSelection() {
    // Auto-select classes (SL, 3E, 3A, 2S, CC if available)
    _selectedClasses = widget.train.autoSelectedClasses.toSet();
  }

  Future<void> _getDeviceId() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      String deviceId;
      
      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        deviceId = androidInfo.id;
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        deviceId = iosInfo.identifierForVendor ?? 'unknown-ios';
      } else {
        deviceId = 'unknown-platform';
      }
      
      setState(() {
        _deviceId = deviceId;
      });
    } catch (e) {
      setState(() {
        _deviceId = 'unknown-device';
      });
    }
  }

  Future<void> _fetchChartTime() async {
    if (_isLoadingChartTime) return;
    
    setState(() {
      _isLoadingChartTime = true;
      _chartTime = null;
    });

    try {
      // Convert journey date from DD-MM-YYYY to YYYY-MM-DD
      final dateParts = widget.journeyDate.split('-');
      final formattedDate = '${dateParts[2]}-${dateParts[1]}-${dateParts[0]}';
      
      final chartTime = await _apiService.getChartTime(
        trainNo: widget.train.trainNumber,
        stationCode: widget.train.fromStnCode,
        journeyDate: formattedDate,
      );
      
      setState(() {
        _chartTime = chartTime;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to fetch chart time: ${e.toString()}'),
            backgroundColor: Colors.orange,
          ),
        );
        setState(() {
          _chartTime = 'Chart time not available';
        });
      }
    } finally {
      setState(() {
        _isLoadingChartTime = false;
      });
    }
  }

  String? _validateTelegramId(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter your Telegram User ID';
    }
    
    if (!RegExp(r'^\d+$').hasMatch(value)) {
      return 'Telegram ID should contain only numbers';
    }
    
    return null;
  }

  Future<void> _submitSelection() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedClasses.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select at least one class'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    if (_chartTime == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fetch chart time first'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      final selection = TrainSelection(
        userId: _deviceId ?? 'unknown',
        telegramId: _telegramIdController.text.trim(),
        fromStation: widget.fromStation,
        toStation: widget.toStation,
        journeyDate: widget.journeyDate,
        trainNo: widget.train.trainNumber,
        chartPrepTime: _chartTime!,
        selectedClasses: _selectedClasses.join(','),
      );

      // Show the final JSON
      _showFinalResult(selection);
      
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isSubmitting = false;
      });
    }
  }

  void _showFinalResult(TrainSelection selection) {
    const jsonEncoder = JsonEncoder.withIndent('  ');
    final jsonString = jsonEncoder.convert([selection.toJson()]);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Selection Complete'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('Your train selection has been processed:'),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: SelectableText(
                  jsonString,
                  style: const TextStyle(
                    fontFamily: 'monospace',
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).popUntil((route) => route.isFirst); // Go to home
            },
            child: const Text('Done'),
          ),
          ElevatedButton(
            onPressed: () {
              Clipboard.setData(ClipboardData(text: jsonString));
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Copied to clipboard')),
              );
            },
            child: const Text('Copy'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Select ${widget.train.trainNumber}'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildTrainInfo(),
              const SizedBox(height: 24),
              _buildTelegramIdField(),
              const SizedBox(height: 24),
              _buildClassSelection(),
              const SizedBox(height: 24),
              _buildChartTimeSection(),
              const SizedBox(height: 32),
              _buildSubmitButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTrainInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${widget.train.trainNumber} - ${widget.train.trainName}',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('From: ${widget.fromStation}'),
                      Text('To: ${widget.toStation}'),
                      Text('Date: ${widget.journeyDate}'),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text('Departure: ${widget.train.departureTime}'),
                    Text('Arrival: ${widget.train.arrivalTime}'),
                    Text('Duration: ${widget.train.duration}'),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTelegramIdField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Telegram User ID',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _telegramIdController,
          keyboardType: TextInputType.number,
          decoration: const InputDecoration(
            labelText: 'Enter your Telegram User ID',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.telegram),
            helperText: 'You can get this from @userinfobot on Telegram',
          ),
          validator: _validateTelegramId,
        ),
      ],
    );
  }

  Widget _buildClassSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Select Classes',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        const Text(
          'Auto-selected: SL, 3E, 3A, 2S, CC (if available)',
          style: TextStyle(fontSize: 12, color: Colors.grey),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: widget.train.avlClasses.map((cls) {
            final isSelected = _selectedClasses.contains(cls);
            final isAutoSelected = widget.train.autoSelectedClasses.contains(cls);

            return FilterChip(
              label: Text(cls),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  if (selected) {
                    _selectedClasses.add(cls);
                  } else {
                    _selectedClasses.remove(cls);
                  }
                });
              },
              backgroundColor: isAutoSelected ? Colors.blue.shade50 : null,
              selectedColor: Colors.blue.shade100,
              checkmarkColor: Colors.blue.shade700,
              labelStyle: TextStyle(
                color: isSelected ? Colors.blue.shade700 : null,
                fontWeight: isAutoSelected ? FontWeight.w600 : null,
              ),
            );
          }).toList(),
        ),
        if (_selectedClasses.isNotEmpty) ...[
          const SizedBox(height: 8),
          Text(
            'Selected: ${_selectedClasses.join(', ')}',
            style: TextStyle(
              fontSize: 12,
              color: Colors.blue.shade700,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildChartTimeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              'Chart Preparation Time',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
            const Spacer(),
            if (!_isLoadingChartTime && _chartTime == null)
              ElevatedButton.icon(
                onPressed: _fetchChartTime,
                icon: const Icon(Icons.access_time, size: 16),
                label: const Text('Fetch Chart Time'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
          ],
        ),
        const SizedBox(height: 8),
        if (_isLoadingChartTime)
          const Row(
            children: [
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
              SizedBox(width: 8),
              Text('Fetching chart time...'),
            ],
          )
        else if (_chartTime != null)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.green.shade50,
              border: Border.all(color: Colors.green.shade200),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Chart Time:',
                  style: TextStyle(fontWeight: FontWeight.w500),
                ),
                const SizedBox(height: 4),
                Text(
                  _chartTime!,
                  style: TextStyle(
                    color: Colors.green.shade700,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Expanded(
                      child: Text(
                        'Chart time fetched successfully',
                        style: TextStyle(fontSize: 12),
                      ),
                    ),
                    TextButton.icon(
                      onPressed: _fetchChartTime,
                      icon: const Icon(Icons.refresh, size: 16),
                      label: const Text('Refresh'),
                      style: TextButton.styleFrom(
                        foregroundColor: Colors.green.shade700,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          )
        else
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.orange.shade50,
              border: Border.all(color: Colors.orange.shade200),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Text(
              'Chart time is required to complete the selection. Please fetch it first.',
              style: TextStyle(color: Colors.orange),
            ),
          ),
      ],
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isSubmitting ? null : _submitSelection,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
        child: _isSubmitting
            ? const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  SizedBox(width: 12),
                  Text('Processing...'),
                ],
              )
            : const Text(
                'Complete Selection',
                style: TextStyle(fontSize: 16),
              ),
      ),
    );
  }
}
