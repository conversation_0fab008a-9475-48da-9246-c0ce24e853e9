import 'package:flutter_test/flutter_test.dart';
import 'package:current_reservation_alerter/models/train_data.dart';

void main() {
  group('Alert Timestamp Tests', () {
    test('should format alert timestamp correctly (convert UTC to local)', () {
      final alertData = {
        'Timestamp': '2025-07-20T18:09:04.762Z',
        'UserID': 'test_user',
        'TelegramID': 123456789,
        'FromStation': 'TEST - TST',
        'ToStation': 'DEST - DST',
        'JourneyDate': '21-07-2025',
        'TrainNo': 12345,
        'ChartPrepTime': '2025-07-21T06:00:00.000Z',
        'Classes': 'SL,3A',
        'Status': 'active',
      };

      final alert = Alert.fromJson(alertData);
      
      // Verify that the formatted timestamp follows the expected pattern
      expect(alert.formattedTimestamp, matches(r'\d{2}-\d{2}-\d{4} \d{2}:\d{2}'));
      
      // Verify that the timestamp is not the same as the original UTC timestamp
      expect(alert.formattedTimestamp, isNot(equals('20-07-2025 18:09')));
    });

    test('should format chart prep time correctly (convert UTC to local)', () {
      final alertData = {
        'Timestamp': '2025-07-20T18:09:04.762Z',
        'UserID': 'test_user',
        'TelegramID': 123456789,
        'FromStation': 'TEST - TST',
        'ToStation': 'DEST - DST',
        'JourneyDate': '21-07-2025',
        'TrainNo': 12345,
        'ChartPrepTime': '2025-07-21T06:00:00.000Z',
        'Classes': 'SL,3A',
        'Status': 'active',
      };

      final alert = Alert.fromJson(alertData);
      
      // Verify that the formatted chart prep time follows the expected pattern
      expect(alert.formattedChartPrepTime, matches(r'\d{1,2}/\d{1,2}/\d{4} \d{2}:\d{2}'));
      
      // Verify that the chart prep time is not the same as the original UTC time
      expect(alert.formattedChartPrepTime, isNot(equals('21/7/2025 06:00')));
    });

    test('should handle invalid timestamp gracefully', () {
      final alertData = {
        'Timestamp': 'invalid-timestamp',
        'UserID': 'test_user',
        'TelegramID': 123456789,
        'FromStation': 'TEST - TST',
        'ToStation': 'DEST - DST',
        'JourneyDate': '21-07-2025',
        'TrainNo': 12345,
        'ChartPrepTime': 'invalid-chart-time',
        'Classes': 'SL,3A',
        'Status': 'active',
      };

      final alert = Alert.fromJson(alertData);
      
      // Should return the original string when parsing fails
      expect(alert.formattedTimestamp, equals('invalid-timestamp'));
      expect(alert.formattedChartPrepTime, equals('invalid-chart-time'));
    });
  });
}
