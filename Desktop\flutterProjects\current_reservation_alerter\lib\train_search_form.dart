import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'rail_api_service.dart';
import 'captcha_dialog.dart';
import 'train_results_screen.dart';
import 'user_logs_screen.dart';

class TrainSearchForm extends StatefulWidget {
  const TrainSearchForm({super.key});

  @override
  State<TrainSearchForm> createState() => _TrainSearchFormState();
}

class _TrainSearchFormState extends State<TrainSearchForm> {
  final _formKey = GlobalKey<FormState>();
  final _fromStationController = TextEditingController();
  final _toStationController = TextEditingController();
  final _dateController = TextEditingController();
  
  final RailApiService _apiService = RailApiService();
  
  bool _isLoading = false;
  bool _isLoadingStations = false;
  List<String> _stations = [];
  List<String> _filteredFromStations = [];
  List<String> _filteredToStations = [];
  
  DateTime? _selectedDate;

  @override
  void initState() {
    super.initState();
    _loadStations();
    
    // Set default date to today
    _selectedDate = DateTime.now();
    _dateController.text = _formatDate(_selectedDate!);
  }

  @override
  void dispose() {
    _fromStationController.dispose();
    _toStationController.dispose();
    _dateController.dispose();
    super.dispose();
  }

  String _formatDate(DateTime date) {
    return "${date.day.toString().padLeft(2, '0')}-${date.month.toString().padLeft(2, '0')}-${date.year}";
  }

  Future<void> _loadStations() async {
    setState(() => _isLoadingStations = true);
    
    try {
      final stations = await _apiService.fetchStationList();
      setState(() {
        _stations = stations;
        _filteredFromStations = stations;
        _filteredToStations = stations;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load stations: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoadingStations = false);
    }
  }

  void _filterFromStations(String query) {
    // Use post-frame callback to avoid setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          if (query.isEmpty) {
            _filteredFromStations = _stations;
          } else {
            _filteredFromStations = _stations
                .where((station) => station.toLowerCase().contains(query.toLowerCase()))
                .toList();
          }
        });
      }
    });
  }

  void _filterToStations(String query) {
    // Use post-frame callback to avoid setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          if (query.isEmpty) {
            _filteredToStations = _stations;
          } else {
            _filteredToStations = _stations
                .where((station) => station.toLowerCase().contains(query.toLowerCase()))
                .toList();
          }
        });
      }
    });
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 120)), // 4 months ahead
      helpText: 'Select Journey Date',
    );
    
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
        _dateController.text = _formatDate(picked);
      });
    }
  }

  String? _validateStation(String? value, String fieldName) {
    if (value == null || value.isEmpty) {
      return 'Please select $fieldName';
    }

    // Check if the entered station exists in our station list (only if stations are loaded)
    if (_stations.isNotEmpty && !_stations.contains(value)) {
      return 'Please select a valid station from the list';
    }

    // Basic format validation - should contain a dash (station name - code format)
    if (!value.contains(' - ')) {
      return 'Station should be in format "STATION NAME - CODE"';
    }

    return null;
  }

  String? _validateDate(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please select a journey date';
    }
    
    if (_selectedDate == null) {
      return 'Please select a valid date';
    }
    
    if (_selectedDate!.isBefore(DateTime.now().subtract(const Duration(days: 1)))) {
      return 'Journey date cannot be in the past';
    }
    
    return null;
  }

  Future<void> _searchTrains() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Validate that stations are different
    if (_fromStationController.text.trim() == _toStationController.text.trim()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('From and To stations cannot be the same'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      // Show progress message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Fetching CAPTCHA...'),
          duration: Duration(seconds: 2),
        ),
      );

      // 1. Fetch the CAPTCHA image
      final captchaBytes = await _apiService.fetchCaptchaImage();

      if (!mounted) return;

      if (captchaBytes == null) {
        throw Exception('Failed to load CAPTCHA. Please check your internet connection and try again.');
      }

      // 2. Show the dialog and get user input
      final captchaInput = await showCaptchaDialog(context, captchaBytes);

      if (!mounted) return;

      if (captchaInput == null || captchaInput.isEmpty) {
        // User cancelled the dialog
        setState(() => _isLoading = false);
        return;
      }

      // Show searching message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Searching for trains...'),
          duration: Duration(seconds: 3),
        ),
      );

      // 3. Make the final API call with the form data
      final responseText = await _apiService.getTrainsBetweenStations(
        captchaInput: captchaInput,
        journeyDate: _dateController.text,
        fromStation: _fromStationController.text,
        toStation: _toStationController.text,
      );

      if (!mounted) return;

      setState(() => _isLoading = false);

      // Hide any existing snackbars
      ScaffoldMessenger.of(context).hideCurrentSnackBar();

      // 4. Navigate to the new screen to display the response
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => TrainResultsScreen(
            responseText: responseText,
            fromStation: _fromStationController.text,
            toStation: _toStationController.text,
            journeyDate: _dateController.text,
          ),
        ),
      );

    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);

        String errorMessage;
        if (e.toString().contains('RailApiException:')) {
          // Extract the message from RailApiException
          errorMessage = e.toString().replaceFirst('RailApiException: ', '');
        } else if (e.toString().contains('SocketException') || e.toString().contains('TimeoutException')) {
          errorMessage = 'Network error. Please check your internet connection and try again.';
        } else if (e.toString().contains('FormatException')) {
          errorMessage = 'Invalid response from server. The CAPTCHA might be incorrect.';
        } else {
          errorMessage = 'An error occurred: ${e.toString()}';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 8),
            action: SnackBarAction(
              label: errorMessage.toLowerCase().contains('captcha') ? 'Retry' : 'Dismiss',
              textColor: Colors.white,
              onPressed: () {
                ScaffoldMessenger.of(context).hideCurrentSnackBar();
                if (errorMessage.toLowerCase().contains('captcha')) {
                  // Retry the search if it was a CAPTCHA error
                  _searchTrains();
                }
              },
            ),
          ),
        );
      }
    }
  }

  Widget _buildStationField({
    required TextEditingController controller,
    required String label,
    required List<String> filteredStations,
    required Function(String) onChanged,
    required String? Function(String?) validator,
  }) {
    return Autocomplete<String>(
      optionsBuilder: (TextEditingValue textEditingValue) {
        onChanged(textEditingValue.text);
        return filteredStations.where((String option) {
          return option.toLowerCase().contains(textEditingValue.text.toLowerCase());
        });
      },
      onSelected: (String selection) {
        controller.text = selection;
      },
      fieldViewBuilder: (context, textEditingController, focusNode, onFieldSubmitted) {
        // Sync the controller
        textEditingController.text = controller.text;
        textEditingController.addListener(() {
          controller.text = textEditingController.text;
        });
        
        return TextFormField(
          controller: textEditingController,
          focusNode: focusNode,
          decoration: InputDecoration(
            labelText: label,
            border: const OutlineInputBorder(),
            suffixIcon: _isLoadingStations 
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: Padding(
                    padding: EdgeInsets.all(12.0),
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                )
              : const Icon(Icons.train),
          ),
          validator: validator,
          onFieldSubmitted: (value) => onFieldSubmitted(),
        );
      },
      optionsViewBuilder: (context, onSelected, options) {
        return Align(
          alignment: Alignment.topLeft,
          child: Material(
            elevation: 4.0,
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxHeight: 200),
              child: ListView.builder(
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemCount: options.length,
                itemBuilder: (BuildContext context, int index) {
                  final String option = options.elementAt(index);
                  return InkWell(
                    onTap: () => onSelected(option),
                    child: Container(
                      padding: const EdgeInsets.all(16.0),
                      child: Text(option),
                    ),
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Find Trains'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const UserLogsScreen(),
                ),
              );
            },
            tooltip: 'View User Logs',
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const Text(
                'Enter Journey Details',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 20),
              
              // From Station Field
              _buildStationField(
                controller: _fromStationController,
                label: 'From Station',
                filteredStations: _filteredFromStations,
                onChanged: _filterFromStations,
                validator: (value) => _validateStation(value, 'from station'),
              ),
              
              const SizedBox(height: 16),
              
              // To Station Field
              _buildStationField(
                controller: _toStationController,
                label: 'To Station',
                filteredStations: _filteredToStations,
                onChanged: _filterToStations,
                validator: (value) => _validateStation(value, 'to station'),
              ),
              
              const SizedBox(height: 16),
              
              // Date Field
              TextFormField(
                controller: _dateController,
                decoration: const InputDecoration(
                  labelText: 'Journey Date',
                  border: OutlineInputBorder(),
                  suffixIcon: Icon(Icons.calendar_today),
                ),
                readOnly: true,
                onTap: _selectDate,
                validator: _validateDate,
              ),
              
              const SizedBox(height: 24),
              
              // Search Button
              ElevatedButton(
                onPressed: _isLoading ? null : _searchTrains,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: _isLoading
                    ? const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          ),
                          SizedBox(width: 12),
                          Text('Searching...'),
                        ],
                      )
                    : const Text(
                        'Search Trains',
                        style: TextStyle(fontSize: 16),
                      ),
              ),
              
              if (_isLoadingStations)
                const Padding(
                  padding: EdgeInsets.only(top: 16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                      SizedBox(width: 8),
                      Text('Loading stations...'),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
