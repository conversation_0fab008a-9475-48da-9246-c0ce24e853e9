import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:current_reservation_alerter/home_screen.dart';

void main() {
  group('HomeScreen Tests', () {
    testWidgets('HomeScreen displays app bar with correct title', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: HomeScreen(),
        ),
      );

      // Verify that the app bar title is displayed
      expect(find.text('Current Reservation Alerter'), findsOneWidget);
      
      // Verify that the refresh button is present
      expect(find.byIcon(Icons.refresh), findsOneWidget);
      
      // Verify that the floating action button is present
      expect(find.byType(FloatingActionButton), findsOneWidget);
    });

    testWidgets('HomeScreen shows loading indicator initially', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: HomeScreen(),
        ),
      );

      // Initially should show loading indicator
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Loading alerts...'), findsOneWidget);
    });

    testWidgets('HomeScreen has menu with correct options', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: HomeScreen(),
        ),
      );

      // Find and tap the popup menu button
      final popupMenuButton = find.byType(PopupMenuButton<String>);
      expect(popupMenuButton, findsOneWidget);
      
      await tester.tap(popupMenuButton);
      await tester.pumpAndSettle();

      // Verify menu items are present
      expect(find.text('Search Trains'), findsOneWidget);
      expect(find.text('View Logs'), findsOneWidget);
    });

    testWidgets('FloatingActionButton has correct properties', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: HomeScreen(),
        ),
      );

      final fab = tester.widget<FloatingActionButton>(find.byType(FloatingActionButton));
      expect(fab.backgroundColor, equals(Colors.blue));
      expect(fab.tooltip, equals('Add New Alert'));
      
      // Verify the icon
      expect(find.byIcon(Icons.add), findsOneWidget);
    });
  });
}
