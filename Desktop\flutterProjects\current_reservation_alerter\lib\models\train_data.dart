import 'package:flutter/material.dart';

class TrainData {
  final int sNo;
  final String trainNumber;
  final String trainName;
  final String fromStnCode;
  final String toStnCode;
  final String arrivalTime;
  final String departureTime;
  final int distance;
  final String duration;
  final bool runningMon;
  final bool runningTue;
  final bool runningWed;
  final bool runningThu;
  final bool runningFri;
  final bool runningSat;
  final bool runningSun;
  final List<String> avlClasses;
  final List<String> trainType;
  final String journeyDate;

  TrainData({
    required this.sNo,
    required this.trainNumber,
    required this.trainName,
    required this.fromStnCode,
    required this.toStnCode,
    required this.arrivalTime,
    required this.departureTime,
    required this.distance,
    required this.duration,
    required this.runningMon,
    required this.runningTue,
    required this.runningWed,
    required this.runningThu,
    required this.runningFri,
    required this.runningSat,
    required this.runningSun,
    required this.avlClasses,
    required this.trainType,
    required this.journeyDate,
  });

  factory TrainData.fromJson(Map<String, dynamic> json) {
    return TrainData(
      sNo: json['sNo'] ?? 0,
      trainNumber: json['trainNumber'] ?? '',
      trainName: json['trainName'] ?? '',
      fromStnCode: json['fromStnCode'] ?? '',
      toStnCode: json['toStnCode'] ?? '',
      arrivalTime: json['arrivalTime'] ?? '',
      departureTime: json['departureTime'] ?? '',
      distance: json['distance'] ?? 0,
      duration: json['duration'] ?? '',
      runningMon: json['runningMon'] == 'Y',
      runningTue: json['runningTue'] == 'Y',
      runningWed: json['runningWed'] == 'Y',
      runningThu: json['runningThu'] == 'Y',
      runningFri: json['runningFri'] == 'Y',
      runningSat: json['runningSat'] == 'Y',
      runningSun: json['runningSun'] == 'Y',
      avlClasses: List<String>.from(json['avlClasses'] ?? []),
      trainType: List<String>.from(json['trainType'] ?? []),
      journeyDate: json['journeyDate'] ?? '',
    );
  }

  // Get auto-selected classes (SL, 3E, 3A, 2S, CC if available)
  List<String> get autoSelectedClasses {
    const autoSelectClasses = ['SL', '3E', '3A', '2S', 'CC'];
    return avlClasses.where((cls) => autoSelectClasses.contains(cls)).toList();
  }

  // Get running days as a readable string
  String get runningDays {
    List<String> days = [];
    if (runningMon) days.add('Mon');
    if (runningTue) days.add('Tue');
    if (runningWed) days.add('Wed');
    if (runningThu) days.add('Thu');
    if (runningFri) days.add('Fri');
    if (runningSat) days.add('Sat');
    if (runningSun) days.add('Sun');
    
    if (days.length == 7) return 'Daily';
    return days.join(', ');
  }
}

class TrainSearchResponse {
  final List<TrainData> trainBtwnStnsList;
  final List<String> quotaList;
  final String timeStamp;
  final Map<String, dynamic> generatedTimeStamp;

  TrainSearchResponse({
    required this.trainBtwnStnsList,
    required this.quotaList,
    required this.timeStamp,
    required this.generatedTimeStamp,
  });

  factory TrainSearchResponse.fromJson(Map<String, dynamic> json) {
    return TrainSearchResponse(
      trainBtwnStnsList: (json['trainBtwnStnsList'] as List?)
          ?.map((train) => TrainData.fromJson(train))
          .toList() ?? [],
      quotaList: List<String>.from(json['quotaList'] ?? []),
      timeStamp: json['timeStamp'] ?? '',
      generatedTimeStamp: Map<String, dynamic>.from(json['generatedTimeStamp'] ?? {}),
    );
  }
}

class TrainSelection {
  final String userId;
  final String telegramId;
  final String fromStation;
  final String toStation;
  final String journeyDate;
  final String trainNo;
  final String chartPrepTime;
  final String selectedClasses;

  TrainSelection({
    required this.userId,
    required this.telegramId,
    required this.fromStation,
    required this.toStation,
    required this.journeyDate,
    required this.trainNo,
    required this.chartPrepTime,
    required this.selectedClasses,
  });

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'telegramId': telegramId,
      'fromStation': fromStation,
      'toStation': toStation,
      'journeyDate': journeyDate,
      'trainNo': trainNo,
      'chartPrepTime': chartPrepTime,
      'selectedClasses': selectedClasses,
    };
  }
}

class Alert {
  final String timestamp;
  final String userId;
  final int telegramId;
  final String fromStation;
  final String toStation;
  final String journeyDate;
  final int trainNo;
  final String chartPrepTime;
  final String classes;
  final String status;

  Alert({
    required this.timestamp,
    required this.userId,
    required this.telegramId,
    required this.fromStation,
    required this.toStation,
    required this.journeyDate,
    required this.trainNo,
    required this.chartPrepTime,
    required this.classes,
    required this.status,
  });

  factory Alert.fromJson(Map<String, dynamic> json) {
    return Alert(
      timestamp: json['Timestamp'] ?? '',
      userId: json['UserID'] ?? '',
      telegramId: json['TelegramID'] ?? 0,
      fromStation: json['FromStation'] ?? '',
      toStation: json['ToStation'] ?? '',
      journeyDate: json['JourneyDate'] ?? '',
      trainNo: json['TrainNo'] ?? 0,
      chartPrepTime: json['ChartPrepTime'] ?? '',
      classes: json['Classes'] ?? '',
      status: json['Status'] ?? '',
    );
  }

  // Helper method to get formatted journey date
  String get formattedJourneyDate {
    try {
      // Parse DD-MM-YYYY format
      final parts = journeyDate.split('-');
      if (parts.length == 3) {
        final day = parts[0];
        final month = parts[1];
        final year = parts[2];
        return '$day/$month/$year';
      }
    } catch (e) {
      // Return original if parsing fails
    }
    return journeyDate;
  }

  // Helper method to get formatted chart prep time (converted to local time)
  String get formattedChartPrepTime {
    try {
      final utcDateTime = DateTime.parse(chartPrepTime);
      final localDateTime = utcDateTime.toLocal();
      return '${localDateTime.day}/${localDateTime.month}/${localDateTime.year} ${localDateTime.hour.toString().padLeft(2, '0')}:${localDateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return chartPrepTime;
    }
  }

  // Helper method to get formatted timestamp (converted to local time)
  String get formattedTimestamp {
    try {
      final utcDateTime = DateTime.parse(timestamp);
      final localDateTime = utcDateTime.toLocal();
      return '${localDateTime.day.toString().padLeft(2, '0')}-${localDateTime.month.toString().padLeft(2, '0')}-${localDateTime.year} ${localDateTime.hour.toString().padLeft(2, '0')}:${localDateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return timestamp;
    }
  }

  // Helper method to get status color
  Color get statusColor {
    switch (status.toLowerCase()) {
      case 'active':
        return Colors.green;
      case 'notified':
        return Colors.orange;
      case 'expired':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
