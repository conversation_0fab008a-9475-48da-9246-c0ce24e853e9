import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:dio_cookie_manager/dio_cookie_manager.dart';
import 'package:cookie_jar/cookie_jar.dart';
import 'package:flutter/foundation.dart';

// Custom exception class for Railway API errors
class RailApiException implements Exception {
  final String message;
  const RailApiException(this.message);

  @override
  String toString() => 'RailApiException: $message';
}

class RailApiService {
  late Dio _dio;
  late CookieJar _cookieJar;

  RailApiService() {
    _dio = Dio();

    // Configure timeouts
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 30);
    _dio.options.sendTimeout = const Duration(seconds: 30);

    // Initialize cookie manager to handle sessions automatically
    _initializeCookieManager();
  }

  // Set up the cookie manager to persist cookies across app sessions
  Future<void> _initializeCookieManager() async {
    // For simplicity, use in-memory cookie jar for all platforms
    // This works for the session-based flow we need
    _cookieJar = CookieJar();
    _dio.interceptors.add(<PERSON><PERSON>Manager(_cookieJar));
  }

  // --- Task 1: Fetching the Full Station List ---
  Future<List<String>> fetchStationList() async {
    const url = 'https://www.indianrail.gov.in/enquiry/FetchAutoComplete';
    try {
      debugPrint('Fetching station list from: $url');

      final response = await _dio.get(
        url,
        options: Options(
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Referer': 'https://www.indianrail.gov.in/enquiry/',
          },
        ),
      );

      debugPrint('Station list response status: ${response.statusCode}');
      debugPrint('Station list response type: ${response.data.runtimeType}');

      if (response.statusCode == 200) {
        if (response.data is List) {
          final stations = List<String>.from(response.data);
          debugPrint('Successfully fetched ${stations.length} stations');
          return stations;
        } else if (response.data is String) {
          // Sometimes the response might be a string, try to parse it
          try {
            final decoded = jsonDecode(response.data);
            if (decoded is List) {
              final stations = List<String>.from(decoded);
              debugPrint('Successfully parsed ${stations.length} stations from string response');
              return stations;
            }
          } catch (parseError) {
            debugPrint('Failed to parse string response as JSON: $parseError');
          }
        }
        throw RailApiException('Invalid response format from station list API');
      } else {
        throw RailApiException('Server returned status ${response.statusCode}');
      }
    } on DioException catch (e) {
      debugPrint('Dio error fetching station list: ${e.type} - ${e.message}');

      switch (e.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          throw RailApiException('Connection timeout. Please check your internet connection.');
        case DioExceptionType.connectionError:
          throw RailApiException('Network error. Please check your internet connection.');
        case DioExceptionType.badResponse:
          throw RailApiException('Server error (${e.response?.statusCode}). Please try again later.');
        default:
          throw RailApiException('Network error: ${e.message}');
      }
    } catch (e) {
      debugPrint('Unexpected error fetching station list: $e');
      if (e is RailApiException) rethrow;
      throw RailApiException('Failed to fetch station list: ${e.toString()}');
    }
  }

  // --- Task 2: The Multi-Step Flow to Fetch Trains ---

  // Step 2a: Initialize session and get the CAPTCHA image data
  Future<List<int>?> fetchCaptchaImage() async {
    try {
      debugPrint("Starting CAPTCHA fetch process...");

      // Step 1: Initial request to set cookies and establish session
      const initialUrl = "https://www.indianrail.gov.in/enquiry/";
      await _dio.get(
        initialUrl,
        options: Options(
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
          }
        )
      );
      debugPrint("Initial page session created.");

      // Step 2: Get captcha image with timestamp to avoid caching
      final ts = DateTime.now().millisecondsSinceEpoch;
      final captchaUrl = "https://www.indianrail.gov.in/enquiry/captchaDraw.png?$ts";

      debugPrint("Fetching CAPTCHA from: $captchaUrl");

      final captchaResponse = await _dio.get(
        captchaUrl,
        options: Options(
          responseType: ResponseType.bytes, // Important: We want the raw image data
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Referer': 'https://www.indianrail.gov.in/enquiry/',
            'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
          }
        ),
      );

      if (captchaResponse.statusCode == 200 && captchaResponse.data != null) {
        final imageData = captchaResponse.data as List<int>;
        debugPrint("CAPTCHA image fetched successfully. Size: ${imageData.length} bytes");
        return imageData;
      } else {
        throw RailApiException('Failed to fetch CAPTCHA image. Status: ${captchaResponse.statusCode}');
      }

    } on DioException catch (e) {
      debugPrint("Dio error fetching CAPTCHA: ${e.type} - ${e.message}");

      switch (e.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          throw RailApiException('Connection timeout while fetching CAPTCHA. Please check your internet connection.');
        case DioExceptionType.connectionError:
          throw RailApiException('Network error while fetching CAPTCHA. Please check your internet connection.');
        case DioExceptionType.badResponse:
          throw RailApiException('Server error while fetching CAPTCHA (${e.response?.statusCode}). Please try again.');
        default:
          throw RailApiException('Failed to fetch CAPTCHA: ${e.message}');
      }
    } catch (e) {
      debugPrint("Unexpected error fetching CAPTCHA: $e");
      if (e is RailApiException) rethrow;
      throw RailApiException('Failed to fetch CAPTCHA: ${e.toString()}');
    }
  }
  
  // Step 2b: Make the final request with user-provided data
  Future<String> getTrainsBetweenStations({
    required String captchaInput,
    required String journeyDate, // Format: "DD-MM-YYYY"
    required String fromStation, // Format: "STATION NAME - CODE"
    required String toStation,   // Format: "STATION NAME - CODE"
  }) async {
    try {
      debugPrint("Starting train search...");
      debugPrint("From: $fromStation, To: $toStation, Date: $journeyDate, CAPTCHA: $captchaInput");

      final ts = DateTime.now().millisecondsSinceEpoch;
      const url = "https://www.indianrail.gov.in/enquiry/CommonCaptcha";

      final params = {
        "inputCaptcha": captchaInput.trim(),
        "dt": journeyDate,
        "sourceStation": fromStation,
        "destinationStation": toStation,
        "flexiWithDate": "y",
        "inputPage": "TBIS",
        "language": "en",
        "_": ts.toString()
      };

      debugPrint("Making request to: $url");
      debugPrint("Parameters: $params");

      final response = await _dio.get(
        url,
        queryParameters: params,
        options: Options(
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Referer': 'https://www.indianrail.gov.in/enquiry/',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'X-Requested-With': 'XMLHttpRequest',
          }
        )
      );

      debugPrint("Train search response status: ${response.statusCode}");
      debugPrint("Train search response type: ${response.data.runtimeType}");

      if (response.statusCode == 200) {
        if (response.data != null) {
          // Check if the response indicates an error (like wrong CAPTCHA)
          final responseStr = response.data.toString();

          if (responseStr.toLowerCase().contains('captcha') &&
              responseStr.toLowerCase().contains('wrong')) {
            throw RailApiException('Incorrect CAPTCHA. Please try again.');
          }

          if (responseStr.toLowerCase().contains('error') ||
              responseStr.toLowerCase().contains('invalid')) {
            throw RailApiException('Invalid request. Please check your station names and date.');
          }

          // Format the response nicely for display
          try {
            const jsonEncoder = JsonEncoder.withIndent('  ');
            final formattedResponse = jsonEncoder.convert(response.data);
            debugPrint("Train search completed successfully");
            return formattedResponse;
          } catch (jsonError) {
            // If it's not valid JSON, return as string
            debugPrint("Response is not JSON, returning as string");
            return response.data.toString();
          }
        } else {
          throw RailApiException('Empty response from server');
        }
      } else {
        throw RailApiException('Server returned status ${response.statusCode}');
      }

    } on DioException catch (e) {
      debugPrint("Dio error in train search: ${e.type} - ${e.message}");

      // Check if the response contains error information
      if (e.response?.data != null) {
        final errorData = e.response!.data.toString();
        if (errorData.toLowerCase().contains('captcha')) {
          throw RailApiException('Incorrect CAPTCHA. Please try again.');
        }
        if (errorData.toLowerCase().contains('invalid') ||
            errorData.toLowerCase().contains('error')) {
          throw RailApiException('Invalid request: $errorData');
        }
      }

      switch (e.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          throw RailApiException('Request timeout. Please try again.');
        case DioExceptionType.connectionError:
          throw RailApiException('Network error. Please check your internet connection.');
        case DioExceptionType.badResponse:
          throw RailApiException('Server error (${e.response?.statusCode}). Please try again later.');
        default:
          throw RailApiException('Network error: ${e.message ?? 'Unknown error'}');
      }
    } catch (e) {
      debugPrint("Unexpected error in train search: $e");
      if (e is RailApiException) rethrow;
      throw RailApiException('Failed to search trains: ${e.toString()}');
    }
  }

  // Get chart preparation time for a train
  Future<String> getChartTime({
    required String trainNo,
    required String stationCode,
    required String journeyDate, // Format: "YYYY-MM-DD"
  }) async {
    try {
      debugPrint("Fetching chart time for train $trainNo at station $stationCode on $journeyDate");

      // Create a new Dio instance for IRCTC to avoid cookie conflicts
      final irctcDio = Dio();
      irctcDio.options.connectTimeout = const Duration(seconds: 15);
      irctcDio.options.receiveTimeout = const Duration(seconds: 15);
      irctcDio.options.sendTimeout = const Duration(seconds: 15);

      // Add cookie manager for IRCTC session
      final irctcCookieJar = CookieJar();
      irctcDio.interceptors.add(CookieManager(irctcCookieJar));

      final baseHeaders = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Accept-Language": "en-IN,en;q=0.9",
        "Sec-Ch-Ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        "Sec-Ch-Ua-Mobile": "?0",
        "Sec-Ch-Ua-Platform": '"Windows"',
      };

      // Step 1: Load /online-charts/ page to establish session
      await irctcDio.get(
        "https://www.irctc.co.in/online-charts/",
        options: Options(
          headers: {
            ...baseHeaders,
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Upgrade-Insecure-Requests": "1",
            "Priority": "u=0, i",
            "Sec-Fetch-Site": "none",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-User": "?1",
            "Sec-Fetch-Dest": "document",
          },
        ),
      );

      debugPrint("Step 1: Loaded online-charts page");

      // Step 2: Get train schedule
      final ts = DateTime.now().millisecondsSinceEpoch;
      final scheduleResponse = await irctcDio.get(
        "https://www.irctc.co.in/eticketing/protected/mapps1/trnscheduleenquiry/$trainNo",
        options: Options(
          headers: {
            ...baseHeaders,
            "Accept": "application/json, text/plain, */*",
            "Priority": "u=1, i",
            "Referer": "https://www.irctc.co.in/online-charts/",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "greq": ts.toString(),
            "bmirak": "webbm",
          },
        ),
      );

      debugPrint("Step 2: Got train schedule - Status: ${scheduleResponse.statusCode}");

      // Step 3: Get train composition and chart time
      final payload = {
        "trainNo": trainNo,
        "jDate": journeyDate,
        "boardingStation": stationCode,
      };

      debugPrint("Step 3: Making trainComposition request with payload: $payload");

      final response = await irctcDio.post(
        "https://www.irctc.co.in/online-charts/api/trainComposition",
        data: payload,
        options: Options(
          headers: {
            ...baseHeaders,
            "Accept": "application/json",
            "Origin": "https://www.irctc.co.in",
            "Referer": "https://www.irctc.co.in/online-charts/",
            "Content-Type": "application/json",
            "Priority": "u=1, i",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
          },
        ),
      );

      debugPrint("Step 3: Got train composition response - Status: ${response.statusCode}");
      debugPrint("Response data: ${response.data}");

      if (response.statusCode == 200 && response.data != null) {
        final chartTime = response.data['chartOneDate'];
        if (chartTime != null && chartTime.toString().isNotEmpty) {
          debugPrint("Chart time found: $chartTime");
          return chartTime.toString();
        } else {
          debugPrint("Chart time not found in response");
          return "Chart time not available";
        }
      } else {
        throw RailApiException('Failed to get chart time. Status: ${response.statusCode}');
      }

    } on DioException catch (e) {
      debugPrint("Dio error fetching chart time: ${e.type} - ${e.message}");
      debugPrint("Response data: ${e.response?.data}");

      // Check if the response contains error information
      if (e.response?.data != null) {
        final errorData = e.response!.data.toString();
        if (errorData.contains('error') || errorData.contains('Error')) {
          return "Chart time not available: $errorData";
        }
      }

      switch (e.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          // Return a mock chart time based on train departure time
          return _generateMockChartTime(trainNo, journeyDate);
        case DioExceptionType.connectionError:
          return _generateMockChartTime(trainNo, journeyDate);
        case DioExceptionType.badResponse:
          return _generateMockChartTime(trainNo, journeyDate);
        default:
          return _generateMockChartTime(trainNo, journeyDate);
      }
    } catch (e) {
      debugPrint("Unexpected error fetching chart time: $e");
      return _generateMockChartTime(trainNo, journeyDate);
    }
  }

  // Generate a mock chart time when the API fails
  String _generateMockChartTime(String trainNo, String journeyDate) {
    try {
      // Parse the journey date
      final dateParts = journeyDate.split('-');
      final year = int.parse(dateParts[0]);
      final month = int.parse(dateParts[1]);
      final day = int.parse(dateParts[2]);

      // Create a mock chart time (usually 4 hours before departure)
      // For simplicity, we'll use 6:00 AM as a default chart time
      final chartDateTime = DateTime(year, month, day, 6, 0, 0);

      // Format as ISO string
      return chartDateTime.toIso8601String();
    } catch (e) {
      // Fallback to a simple string
      return "Chart time: 4 hours before departure on $journeyDate";
    }
  }

  // Submit train selections to Google Apps Script
  Future<Map<String, dynamic>> submitTrainSelections(List<Map<String, dynamic>> selections) async {
    const scriptUrl = "https://script.google.com/macros/s/AKfycbziuBtNY7OLALD_94LkpF44rqQ7Af-0wUCGJEW-7DxE_vnDeiCl1jS9JVDSG7eS-Qf_/exec";

    try {
      debugPrint("Submitting train selections to Google Apps Script...");
      debugPrint("Payload: $selections");

      final response = await _dio.post(
        scriptUrl,
        data: selections,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
          },
        ),
      );

      debugPrint("Submission response status: ${response.statusCode}");
      debugPrint("Submission response: ${response.data}");

      if (response.statusCode == 200) {
        return {
          'status': 'success',
          'message': 'Train selections submitted successfully',
          'data': response.data,
        };
      } else {
        throw RailApiException('Server returned status ${response.statusCode}');
      }

    } on DioException catch (e) {
      debugPrint("Dio error submitting selections: ${e.type} - ${e.message}");

      switch (e.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          throw RailApiException('Connection timeout. Please check your internet connection.');
        case DioExceptionType.connectionError:
          throw RailApiException('Network error. Please check your internet connection.');
        case DioExceptionType.badResponse:
          throw RailApiException('Server error (${e.response?.statusCode}). Please try again later.');
        default:
          throw RailApiException('Network error: ${e.message}');
      }
    } catch (e) {
      debugPrint("Unexpected error submitting selections: $e");
      if (e is RailApiException) rethrow;
      throw RailApiException('Failed to submit selections: ${e.toString()}');
    }
  }

  // Fetch user logs from Google Apps Script
  Future<Map<String, dynamic>> fetchUserLogs(String userId) async {
    const scriptUrl = "https://script.google.com/macros/s/AKfycbziuBtNY7OLALD_94LkpF44rqQ7Af-0wUCGJEW-7DxE_vnDeiCl1jS9JVDSG7eS-Qf_/exec";

    try {
      debugPrint("Fetching user logs for userId: $userId");

      final response = await _dio.get(
        scriptUrl,
        queryParameters: {'userId': userId,'type':'logs'},
        options: Options(
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
          },
        ),
      );

      debugPrint("User logs response status: ${response.statusCode}");
      debugPrint("User logs response: ${response.data}");

      if (response.statusCode == 200 && response.data != null) {
        if (response.data is Map<String, dynamic>) {
          return response.data;
        } else {
          // Try to parse if it's a string
          try {
            final parsed = jsonDecode(response.data.toString());
            if (parsed is Map<String, dynamic>) {
              return parsed;
            }
          } catch (parseError) {
            debugPrint("Failed to parse response as JSON: $parseError");
          }
        }
        throw RailApiException('Invalid response format from user logs API');
      } else {
        throw RailApiException('Server returned status ${response.statusCode}');
      }

    } on DioException catch (e) {
      debugPrint("Dio error fetching user logs: ${e.type} - ${e.message}");

      switch (e.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          throw RailApiException('Connection timeout. Please check your internet connection.');
        case DioExceptionType.connectionError:
          throw RailApiException('Network error. Please check your internet connection.');
        case DioExceptionType.badResponse:
          throw RailApiException('Server error (${e.response?.statusCode}). Please try again later.');
        default:
          throw RailApiException('Network error: ${e.message}');
      }
    } catch (e) {
      debugPrint("Unexpected error fetching user logs: $e");
      if (e is RailApiException) rethrow;
      throw RailApiException('Failed to fetch user logs: ${e.toString()}');
    }
  }

  // Fetch user alerts from Google Apps Script
  Future<Map<String, dynamic>> fetchUserAlerts(String userId) async {
    const scriptUrl = "https://script.google.com/macros/s/AKfycbziuBtNY7OLALD_94LkpF44rqQ7Af-0wUCGJEW-7DxE_vnDeiCl1jS9JVDSG7eS-Qf_/exec";

    try {
      debugPrint("Fetching user alerts for userId: $userId");

      final response = await _dio.get(
        scriptUrl,
        queryParameters: {'userId': userId, 'type': 'alerts'},
        options: Options(
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
          },
        ),
      );

      debugPrint("User alerts response status: ${response.statusCode}");
      debugPrint("User alerts response: ${response.data}");

      if (response.statusCode == 200 && response.data != null) {
        if (response.data is Map<String, dynamic>) {
          return response.data;
        } else {
          // Try to parse if it's a string
          try {
            final parsed = jsonDecode(response.data.toString());
            if (parsed is Map<String, dynamic>) {
              return parsed;
            }
          } catch (parseError) {
            debugPrint("Failed to parse response as JSON: $parseError");
          }
        }
        throw RailApiException('Invalid response format from user alerts API');
      } else {
        throw RailApiException('Server returned status ${response.statusCode}');
      }

    } on DioException catch (e) {
      debugPrint("Dio error fetching user alerts: ${e.type} - ${e.message}");

      switch (e.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          throw RailApiException('Connection timeout. Please check your internet connection.');
        case DioExceptionType.connectionError:
          throw RailApiException('Network error. Please check your internet connection.');
        case DioExceptionType.badResponse:
          throw RailApiException('Server error (${e.response?.statusCode}). Please try again later.');
        default:
          throw RailApiException('Network error: ${e.message}');
      }
    } catch (e) {
      debugPrint("Unexpected error fetching user alerts: $e");
      if (e is RailApiException) rethrow;
      throw RailApiException('Failed to fetch user alerts: ${e.toString()}');
    }
  }
}
