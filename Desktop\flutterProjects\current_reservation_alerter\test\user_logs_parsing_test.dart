import 'package:flutter_test/flutter_test.dart';

void main() {
  group('User Logs Data Parsing Tests', () {
    test('should correctly parse actual log data structure', () {
      // This is the actual data structure you're receiving from Google Apps Script
      final sampleLogData = {
        "status": "success",
        "logs": [
          {
            "LogTimestamp": "2025-07-20T18:09:04.762Z",
            "UserID": "RKQ1.201217.002",
            "FinalStatus": "Not Available",
            "Details": "Status for SL: NOT AVAILABLE",
            "TelegramID": "512930889",
            "FromStation": "CHIRALA - CLX",
            "ToStation": "SECUNDERABAD JN - SC",
            "JourneyDate": "21-07-2025",
            "TrainNo": "17229",
            "ClassesQueried": "SL,3A"
          },
          {
            "LogTimestamp": "2025-07-20T18:55:55.053Z",
            "UserID": "RKQ1.201217.002",
            "FinalStatus": "Expired (Stale)",
            "Details": "Alert removed by cleanup rule (>1 hr past chart prep time).",
            "TelegramID": "512930889",
            "FromStation": "CHIRALA - CLX",
            "ToStation": "SECUNDERABAD JN - SC",
            "JourneyDate": "21-07-2025",
            "TrainNo": "17405",
            "ClassesQueried": "2S,SL,3A"
          },
          {
            "LogTimestamp": "2025-07-20T18:59:57.745Z",
            "UserID": "RKQ1.201217.002",
            "FinalStatus": "Notified",
            "Details": "Seat Found! Status for SL: CURR_AVBL-0018",
            "TelegramID": "512930889",
            "FromStation": "TIRUPATI - TPTY",
            "ToStation": "CHIRALA - CLX",
            "JourneyDate": "21-07-2025",
            "TrainNo": "17405",
            "ClassesQueried": "2S,SL,3A"
          }
        ]
      };

      // Test that we can extract the logs array
      expect(sampleLogData['status'], equals('success'));
      expect(sampleLogData['logs'], isA<List>());
      
      final logs = sampleLogData['logs'] as List<dynamic>;
      expect(logs.length, equals(3));

      // Test parsing of individual log entries
      final firstLog = logs[0] as Map<String, dynamic>;
      
      // Test field extraction using the actual field names
      final trainNo = firstLog['TrainNo'] ?? firstLog['trainNo'] ?? 'Unknown';
      final fromStation = firstLog['FromStation'] ?? firstLog['fromStation'] ?? 'Unknown';
      final toStation = firstLog['ToStation'] ?? firstLog['toStation'] ?? 'Unknown';
      final journeyDate = firstLog['JourneyDate'] ?? firstLog['journeyDate'] ?? 'Unknown';
      final selectedClasses = firstLog['ClassesQueried'] ?? firstLog['selectedClasses'] ?? 'Unknown';
      final telegramId = firstLog['TelegramID'] ?? firstLog['telegramId'] ?? 'Unknown';
      final logTimestamp = firstLog['LogTimestamp'] ?? firstLog['logTimestamp'] ?? 'Unknown';
      final finalStatus = firstLog['FinalStatus'] ?? firstLog['finalStatus'] ?? 'Unknown';
      final details = firstLog['Details'] ?? firstLog['details'] ?? 'No details available';

      // Verify the extracted values
      expect(trainNo, equals('17229'));
      expect(fromStation, equals('CHIRALA - CLX'));
      expect(toStation, equals('SECUNDERABAD JN - SC'));
      expect(journeyDate, equals('21-07-2025'));
      expect(selectedClasses, equals('SL,3A'));
      expect(telegramId, equals('512930889'));
      expect(logTimestamp, equals('2025-07-20T18:09:04.762Z'));
      expect(finalStatus, equals('Not Available'));
      expect(details, equals('Status for SL: NOT AVAILABLE'));

      // Test timestamp parsing
      final dateTime = DateTime.parse(logTimestamp);
      expect(dateTime.year, equals(2025));
      expect(dateTime.month, equals(7));
      expect(dateTime.day, equals(20));
    });

    test('should handle different status types correctly', () {
      final logs = [
        {"FinalStatus": "Not Available"},
        {"FinalStatus": "Expired (Stale)"},
        {"FinalStatus": "Notified"},
        {"FinalStatus": "Some Other Status"},
      ];

      for (final log in logs) {
        final finalStatus = log['FinalStatus'] ?? 'Unknown';
        
        // Test status categorization logic
        if (finalStatus.toLowerCase().contains('notified')) {
          expect(finalStatus, contains('Notified'));
        } else if (finalStatus.toLowerCase().contains('expired') || 
                   finalStatus.toLowerCase().contains('stale')) {
          expect(finalStatus, anyOf(contains('Expired'), contains('Stale')));
        } else if (finalStatus.toLowerCase().contains('not available')) {
          expect(finalStatus, contains('Not Available'));
        }
      }
    });

    test('should format timestamp correctly (convert UTC to local)', () {
      final testTimestamp = "2025-07-20T18:09:04.762Z";

      final utcDateTime = DateTime.parse(testTimestamp);
      final localDateTime = utcDateTime.toLocal();
      final formattedTimestamp = '${localDateTime.day.toString().padLeft(2, '0')}-${localDateTime.month.toString().padLeft(2, '0')}-${localDateTime.year} ${localDateTime.hour.toString().padLeft(2, '0')}:${localDateTime.minute.toString().padLeft(2, '0')}';

      // Note: The expected result will vary based on the local timezone
      // This test verifies the format is correct, not the exact time
      expect(formattedTimestamp, matches(r'\d{2}-\d{2}-\d{4} \d{2}:\d{2}'));
    });
  });
}
